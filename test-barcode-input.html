<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>条码输入测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            font-size: 18px;
            text-align: center;
            border: 2px solid #ccc;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .result {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>条码输入限制测试</h1>
    
    <div class="info">
        <h3>测试要求：</h3>
        <ul>
            <li>只能输入数字</li>
            <li>最多14位</li>
            <li>自动过滤非数字字符</li>
        </ul>
    </div>

    <label for="barcode">条码输入框：</label>
    <input 
        id="barcode" 
        class="test-input" 
        type="text" 
        placeholder="请输入条码" 
        maxlength="14"
    >
    
    <div class="result">
        <p><strong>当前值：</strong><span id="current-value">无</span></p>
        <p><strong>字符长度：</strong><span id="length">0</span></p>
        <p><strong>是否全为数字：</strong><span id="is-numeric">是</span></p>
    </div>

    <div class="info">
        <h3>测试用例：</h3>
        <ul>
            <li>输入 "123abc456" → 应该显示 "123456"</li>
            <li>输入 "12345678901234567890" → 应该显示 "12345678901234"（14位）</li>
            <li>输入 "abc!@#$%^&*()" → 应该显示 ""（空）</li>
            <li>输入 "123-456-789" → 应该显示 "123456789"</li>
        </ul>
    </div>

    <script>
        const barcodeInput = document.getElementById('barcode');
        const currentValueSpan = document.getElementById('current-value');
        const lengthSpan = document.getElementById('length');
        const isNumericSpan = document.getElementById('is-numeric');

        function handleBarcodeInput(event) {
            // 获取输入值
            let value = event.target.value;
            
            // 只保留数字
            value = value.replace(/\D/g, '');
            
            // 限制最大长度为14位
            if (value.length > 14) {
                value = value.slice(0, 14);
            }
            
            // 更新输入框的值
            event.target.value = value;
            
            // 更新显示
            updateDisplay(value);
        }

        function updateDisplay(value) {
            currentValueSpan.textContent = value || '无';
            lengthSpan.textContent = value.length;
            isNumericSpan.textContent = /^\d*$/.test(value) ? '是' : '否';
        }

        barcodeInput.addEventListener('input', handleBarcodeInput);
        
        // 初始化显示
        updateDisplay('');
    </script>
</body>
</html>
